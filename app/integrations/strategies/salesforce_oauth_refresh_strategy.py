from typing import Any

from simple_salesforce.exceptions import SalesforceExpiredSession

from app.common.helpers.logger import get_logger
from app.integrations.adapters.salesforce.client import SalesforceClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.token_refresh_strategy import ITokenRefreshStrategy

logger = get_logger()


class SalesforceOAuthRefreshStrategy(ITokenRefreshStrategy):
    """Token refresh strategy for Salesforce OAuth integrations."""
    
    def should_refresh(self, exception: Exception) -> bool:
        """Check if the exception indicates an expired Salesforce session."""
        return isinstance(exception, SalesforceExpiredSession)
    
    async def refresh_credentials(self, credentials: ICredentials) -> ICredentials:
        """Refresh Salesforce OAuth credentials."""
        logger.info("Refreshing Salesforce token")
        refreshed_credentials = await credentials.refresh_token()
        logger.info("Salesforce token refreshed successfully")
        return refreshed_credentials
    
    def reinitialize_client(self, instance: Any, credentials: ICredentials) -> None:
        """Reinitialize Salesforce client with refreshed credentials."""
        if hasattr(instance, 'salesforce_client') and isinstance(instance.salesforce_client, SalesforceClient):
            logger.info("Re-initializing Salesforce client with refreshed token")
            instance._init_salesforce_client(credentials)
        else:
            logger.warning("Instance does not have a Salesforce client to reinitialize")