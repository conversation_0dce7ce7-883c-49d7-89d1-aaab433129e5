import pytest
from unittest.mock import Mock

from simple_salesforce.exceptions import SalesforceExpiredSession

from app.integrations.adapters.salesforce.client import SalesforceClient
from app.integrations.strategies.salesforce_oauth_refresh_strategy import SalesforceOAuthRefreshStrategy


class TestSalesforceOAuthRefreshStrategy:
    def test_should_refresh_expired_session(self):
        """Test that SalesforceExpiredSession triggers refresh."""
        strategy = SalesforceOAuthRefreshStrategy()
        error = SalesforceExpiredSession("Session expired")
        
        assert strategy.should_refresh(error) is True
    
    def test_should_refresh_other_exception(self):
        """Test that other exceptions don't trigger refresh."""
        strategy = SalesforceOAuthRefreshStrategy()
        error = ValueError("Some other error")
        
        assert strategy.should_refresh(error) is False
    
    @pytest.mark.asyncio
    async def test_refresh_credentials(self):
        """Test that credentials are refreshed correctly."""
        strategy = SalesforceOAuthRefreshStrategy()
        
        mock_credentials = Mock()
        refreshed_credentials = Mock()
        mock_credentials.refresh_token = Mock(return_value=refreshed_credentials)
        
        result = await strategy.refresh_credentials(mock_credentials)
        
        mock_credentials.refresh_token.assert_called_once()
        assert result == refreshed_credentials
    
    def test_reinitialize_client_with_salesforce_client(self):
        """Test that Salesforce client is reinitialized correctly."""
        strategy = SalesforceOAuthRefreshStrategy()
        
        mock_instance = Mock()
        mock_client = Mock(spec=SalesforceClient)
        mock_instance.salesforce_client = mock_client
        mock_instance._init_salesforce_client = Mock()
        
        mock_credentials = Mock()
        
        strategy.reinitialize_client(mock_instance, mock_credentials)
        
        mock_instance._init_salesforce_client.assert_called_once_with(mock_credentials)
    
    def test_reinitialize_client_without_client(self):
        """Test that reinitialize handles instances without Salesforce client."""
        strategy = SalesforceOAuthRefreshStrategy()
        
        mock_instance = Mock()
        # No salesforce_client attribute
        del mock_instance.salesforce_client
        
        mock_credentials = Mock()
        
        # Should not raise an exception
        strategy.reinitialize_client(mock_instance, mock_credentials)