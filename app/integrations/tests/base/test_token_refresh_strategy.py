import pytest
from unittest.mock import AsyncMock, Mock

from app.integrations.base.token_refresh_strategy import ITokenRefreshStrategy, handle_token_expiry


class MockTokenRefreshStrategy(ITokenRefreshStrategy):
    def __init__(self, should_refresh_result: bool = True):
        self.should_refresh_result = should_refresh_result
        self.refresh_credentials = AsyncMock()
        self.reinitialize_client = Mock()
    
    def should_refresh(self, exception: Exception) -> bool:
        return self.should_refresh_result


class MockInstance:
    def __init__(self):
        self.credentials = Mock()


class TestTokenRefreshStrategy:
    @pytest.mark.asyncio
    async def test_handle_token_expiry_no_exception(self):
        """Test that decorator passes through when no exception occurs."""
        strategy = MockTokenRefreshStrategy()
        
        @handle_token_expiry(strategy)
        async def test_method(self):
            return "success"
        
        instance = MockInstance()
        result = await test_method(instance)
        
        assert result == "success"
        strategy.refresh_credentials.assert_not_called()
        strategy.reinitialize_client.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_handle_token_expiry_with_refresh(self):
        """Test that decorator handles token refresh when needed."""
        strategy = MockTokenRefreshStrategy(should_refresh_result=True)
        refreshed_credentials = Mock()
        strategy.refresh_credentials.return_value = refreshed_credentials
        
        call_count = 0
        
        @handle_token_expiry(strategy)
        async def test_method(self):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ValueError("Token expired")
            return "success after refresh"
        
        instance = MockInstance()
        result = await test_method(instance)
        
        assert result == "success after refresh"
        assert call_count == 2  # Called twice: first fails, second succeeds
        strategy.refresh_credentials.assert_called_once_with(instance.credentials)
        strategy.reinitialize_client.assert_called_once_with(instance, refreshed_credentials)
        assert instance.credentials == refreshed_credentials
    
    @pytest.mark.asyncio
    async def test_handle_token_expiry_no_refresh_needed(self):
        """Test that decorator doesn't refresh when strategy says not to."""
        strategy = MockTokenRefreshStrategy(should_refresh_result=False)
        
        @handle_token_expiry(strategy)
        async def test_method(self):
            raise ValueError("Some other error")
        
        instance = MockInstance()
        
        with pytest.raises(ValueError, match="Some other error"):
            await test_method(instance)
        
        strategy.refresh_credentials.assert_not_called()
        strategy.reinitialize_client.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_handle_token_expiry_with_chained_exceptions(self):
        """Test that decorator handles chained exceptions correctly."""
        strategy = MockTokenRefreshStrategy(should_refresh_result=True)
        refreshed_credentials = Mock()
        strategy.refresh_credentials.return_value = refreshed_credentials
        
        call_count = 0
        
        @handle_token_expiry(strategy)
        async def test_method(self):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                # Create a chained exception
                try:
                    raise ValueError("Token expired")
                except ValueError as e:
                    raise RuntimeError("Wrapper error") from e
            return "success after refresh"
        
        instance = MockInstance()
        result = await test_method(instance)
        
        assert result == "success after refresh"
        assert call_count == 2
        strategy.refresh_credentials.assert_called_once_with(instance.credentials)
        strategy.reinitialize_client.assert_called_once_with(instance, refreshed_credentials)