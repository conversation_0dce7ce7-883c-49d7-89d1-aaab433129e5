from abc import ABC, abstractmethod

from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.types import IntegrationSource


class BaseAdapter(ABC):
    def __init__(self, credentials: ICredentials):
        self._credentials = credentials

    @property
    @abstractmethod
    def source(self) -> IntegrationSource:
        pass

    @property
    def credentials(self) -> ICredentials:
        return self._credentials

    @credentials.setter
    def credentials(self, value: ICredentials) -> None:
        self._credentials = value
