from datetime import date, datetime
from typing import Any

from pydantic import BaseModel


class MessageData(BaseModel):
    message_id: str
    channel_id: str
    content: str
    sent_at: datetime
    last_edit_at: datetime | None = None
    tombstone: bool | None = False
    author: str | None = None
    thread_id: str | None = None
    parent_id: str | None = None


class MessageChangelogData(BaseModel):
    channel_id: str
    operation: str
    message_id: str
    cursor_id: int
    created_at: datetime


class ChannelDataSlice(BaseModel):
    channel_id: str
    messages: list[MessageData]
    from_time: datetime
    to_time: datetime


class ReconciliationStats(BaseModel):
    inserts: int
    updates: int
    deletes: int


class ChannelIngestionResult(BaseModel):
    messages_count: int
    inserts: int
    updates: int
    deletes: int
    from_time: datetime
    to_time: datetime


class ChannelProcessingResult(BaseModel):
    processed_changes: int
    regenerated_documents: int
    deleted_documents: int


class DocumentData(BaseModel):
    id: str
    content: str
    source_timestamp: datetime
    tags: set[str]


class CRMAccountAccessData(BaseModel):
    account_id: str
    account_name: str
    access_type: str
    access_role: str | None


class CRMAccountAccessSlice(BaseModel):
    user_id: str
    accounts: list[CRMAccountAccessData]


class CRMAccountAccessSyncResult(BaseModel):
    new_access_count: int
    old_access_count: int


class FileData(BaseModel):
    id: str
    name: str
    size: int
    time_created: datetime
    last_modified: datetime
    md5_hash: str
    content_type: str


class FileProcessingResult(BaseModel):
    processed_files: int
    deleted_documents: int


class CalendarEventAttendee(BaseModel):
    email: str
    name: str | None = None
    response_status: str | None = None
    is_organizer: bool = False
    is_optional: bool = False


class CalendarEventDateTime(BaseModel):
    date_time: datetime | None = None
    day: date | None = None
    timezone: str | None = None


class CalendarEventRecurrence(BaseModel):
    frequency: str
    interval: int = 1
    count: int | None = None
    until: datetime | None = None
    by_day: list[str] | None = None
    by_month_day: list[int] | None = None
    by_month: list[int] | None = None


class CalendarEvent(BaseModel):
    id: str
    calendar_id: str
    title: str
    description: str | None = None
    location: str | None = None
    start: CalendarEventDateTime | None = None
    end: CalendarEventDateTime | None = None
    all_day: bool = False
    attendees: list[CalendarEventAttendee] = []
    organizer: CalendarEventAttendee | None = None
    recurrence: CalendarEventRecurrence | None = None
    status: str = "confirmed"
    visibility: str = "default"
    created_at: datetime | None = None
    updated_at: datetime | None = None
    html_link: str | None = None
    meeting_url: str | None = None
    raw_data: dict[str, Any] | None = None


class CalendarEventListWithToken(BaseModel):
    events: list[CalendarEvent]
    next_page_token: str | None = None


class Calendar(BaseModel):
    id: str
    name: str
    description: str | None = None
    timezone: str | None = None
    is_primary: bool = False
    access_role: str = "reader"
    color_id: str | None = None
    background_color: str | None = None
    foreground_color: str | None = None
    raw_data: dict[str, Any] | None = None
